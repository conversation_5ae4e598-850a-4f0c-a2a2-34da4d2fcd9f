

<?php $__env->startPush('styles'); ?>
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        /* Custom Flatpickr styling for modern look */
        .flatpickr-calendar {
            border-radius: 8px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
            border: 1px solid #e0e0e0 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        }

        .flatpickr-months {
            background: #f8f9fa !important;
            border-radius: 8px 8px 0 0 !important;
        }

        .flatpickr-current-month {
            color: #333 !important;
            font-weight: 600 !important;
        }

        .flatpickr-weekday {
            color: #666 !important;
            font-weight: 500 !important;
            font-size: 12px !important;
        }

        .flatpickr-day {
            border-radius: 6px !important;
            margin: 1px !important;
            transition: all 0.2s ease !important;
        }

        .flatpickr-day:hover {
            background: #e3f2fd !important;
            border-color: #2196f3 !important;
        }

        .flatpickr-day.selected {
            background: #2196f3 !important;
            border-color: #2196f3 !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            text-shadow: none !important;
            box-shadow: none !important;
        }

        .flatpickr-day.selected:hover {
            background: #1976d2 !important;
            border-color: #1976d2 !important;
            color: #ffffff !important;
            text-shadow: none !important;
            box-shadow: none !important;
        }

        .flatpickr-day.today {
            border-color: #2196f3 !important;
            color: #2196f3 !important;
            font-weight: 600 !important;
        }

        .flatpickr-prev-month,
        .flatpickr-next-month {
            color: #666 !important;
            transition: color 0.2s ease !important;
        }

        .flatpickr-prev-month:hover,
        .flatpickr-next-month:hover {
            color: #2196f3 !important;
        }

        /* Ensure selected date text is always visible */
        .flatpickr-day.selected,
        .flatpickr-day.selected:focus,
        .flatpickr-day.selected:active {
            background-color: #2196f3 !important;
            color: #ffffff !important;
            text-shadow: none !important;
            box-shadow: none !important;
            opacity: 1 !important;
        }

        .flatpickr-day.selected:hover,
        .flatpickr-day.selected:focus:hover {
            background-color: #1976d2 !important;
            color: #ffffff !important;
            text-shadow: none !important;
            box-shadow: none !important;
            opacity: 1 !important;
        }
    </style>
    <style>
        .select2-container .select2-selection--single {
            display: none !important;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            display: none !important;
        }

        .select2-dropdown {
            border: 2px solid #5c5d5e;
            border-radius: 5px;
            box-shadow: 0 4px 12px #5c5d5e;
            z-index: 9999 !important;
            background: white !important;
        }

        /* Fix for dropdown containers - make them invisible */
        .medication-external-dropdown,
        .ship-to-external-dropdown,
        .gender-external-dropdown,
        .state-external-dropdown {
            z-index: 9999 !important;
            background: transparent !important;
            border: none !important;
            border-radius: 0 !important;
            box-shadow: none !important;
            padding: 0 !important;
        }

        /* Ensure Select2 results container has proper z-index */
        .select2-results {
            z-index: 9999 !important;
            background: white !important;
        }

        /* Fix for any overlapping elements */
        .select2-container--open .select2-dropdown {
            z-index: 9999 !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const continueBtn = document.getElementById('continue-btn');
            if (continueBtn) {
                continueBtn.addEventListener('click', function() {
                    this.disabled = true;
                    this.classList.add('processing');
                    this.innerHTML =
                        '<div class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"><span class="sr-only">Loading...</span></div> Processing...';
                    this.form.submit(); // optional: ensures the form still submits
                });
            }

            // Initialize error text hover functionality
            initializeErrorTextHover();

            // Inline editing functionality
            initializeInlineEditing();

            // Initialize Flatpickr for modal date inputs
            initializeFlatpickrModals();
        });

        function initializeInlineEditing() {
            // Add click event to editable cells and their text content
            document.querySelectorAll('.editable-cell').forEach(function(cell) {
                // Function to handle editing for both cell and text clicks
                const handleEdit = function(event) {
                    // Prevent event bubbling to avoid double triggers
                    event.stopPropagation();

                    if (cell.classList.contains('editing')) return;

                    const originalValue = cell.textContent.trim();
                    const originalHTML = cell.innerHTML; // Store the original HTML including styling
                    const rowIndex = cell.dataset.rowIndex;
                    const columnIndex = cell.dataset.columnIndex;

                    // Check if this is the Script Date column (index 0)
                    if (parseInt(columnIndex) === 0) {
                        createInlineDatePicker(cell, rowIndex, columnIndex, originalValue,
                            true); // true = restrict future dates
                        return;
                    }

                    // Check if this is the DOB column (index 3)
                    if (parseInt(columnIndex) === 3) {
                        createInlineDatePicker(cell, rowIndex, columnIndex, originalValue,
                            true); // true = restrict future dates
                        return;
                    }

                    // Check if this is the Gender column (index 4)
                    if (parseInt(columnIndex) === 4) {
                        openGenderInlineDropdown(cell, rowIndex, columnIndex, originalValue);
                        return;
                    }

                    // Check if this is the medication column (index 10)
                    if (parseInt(columnIndex) === 10) {
                        openMedicationInlineDropdown(cell, rowIndex, columnIndex, originalValue);
                        return;
                    }

                    // Check if this is the State column (index 7)
                    if (parseInt(columnIndex) === 7) {
                        openStateInlineDropdown(cell, rowIndex, columnIndex, originalValue);
                        return;
                    }

                    // Check if this is the Ship To column (index 16)
                    if (parseInt(columnIndex) === 16) {
                        openShipToInlineDropdown(cell, rowIndex, columnIndex, originalValue);
                        return;
                    }

                    // Capture current cell dimensions before editing
                    const cellRect = cell.getBoundingClientRect();
                    const computedStyle = window.getComputedStyle(cell);
                    const cellWidth = cellRect.width;
                    const cellHeight = cellRect.height;

                    // Set CSS custom properties to lock dimensions during editing
                    cell.style.setProperty('--cell-width', cellWidth + 'px');
                    cell.style.setProperty('--cell-height', cellHeight + 'px');

                    // Create input element
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.value = originalValue;
                    // Remove Bootstrap classes to prevent size changes
                    input.className = '';
                    input.style.width = '100%';
                    input.style.height = '100%';
                    input.style.border = 'none';
                    input.style.outline = 'none';
                    input.style.background = 'transparent';
                    input.style.padding = '0';
                    input.style.margin = '0';
                    input.style.fontSize = 'inherit';
                    input.style.fontFamily = 'inherit';
                    input.style.lineHeight = 'inherit';

                    // Replace cell content with input
                    cell.innerHTML = '';
                    cell.appendChild(input);
                    cell.classList.add('editing');

                    // Focus and select text
                    input.focus();
                    input.select();

                    // Handle save on Enter or blur
                    const saveEdit = function() {
                        const newValue = input.value.trim();

                        // Only update if the value actually changed
                        if (newValue !== originalValue) {
                            updateCellValue(cell, rowIndex, columnIndex, newValue, originalValue);
                        } else {
                            // Value didn't change, restore the original HTML (including error styling)
                            cell.innerHTML = originalHTML;
                            cell.classList.remove('editing');
                            // Remove dimension locks
                            cell.style.removeProperty('--cell-width');
                            cell.style.removeProperty('--cell-height');
                            // Re-initialize editing and hover events for restored content
                            initializeInlineEditing();
                            initializeErrorTextHover();
                        }
                    };

                    // Handle cancel on Escape
                    const cancelEdit = function() {
                        cell.innerHTML = originalHTML;
                        cell.classList.remove('editing');
                        // Remove dimension locks
                        cell.style.removeProperty('--cell-width');
                        cell.style.removeProperty('--cell-height');
                        // Re-initialize editing and hover events for restored content
                        initializeInlineEditing();
                        initializeErrorTextHover();
                    };

                    input.addEventListener('blur', saveEdit);
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            saveEdit();
                        } else if (e.key === 'Escape') {
                            e.preventDefault();
                            cancelEdit();
                        }
                    });
                };

                // Add click event to the cell itself
                cell.addEventListener('click', handleEdit);

                // Make all text content within the cell clickable
                makeTextContentClickable(cell, handleEdit);
            });
        }

        // Function to initialize Flatpickr for modal date inputs
        function initializeFlatpickrModals() {
            // Initialize Flatpickr for Script Date modal input
            if (document.getElementById('script_date_input')) {
                flatpickr('#script_date_input', {
                    dateFormat: "m/d/Y",
                    allowInput: true,
                    clickOpens: true,
                    defaultDate: new Date() // Default to today for Script Date
                });
            }

            // Initialize Flatpickr for DOB modal input with future date restriction
            if (document.getElementById('dob_date_input')) {
                flatpickr('#dob_date_input', {
                    dateFormat: "m/d/Y",
                    allowInput: true,
                    clickOpens: true,
                    maxDate: "today",
                    defaultDate: "1/1/1980" // Default to 1/1/1980 for DOB
                });
            }
        }

        // Helper function to make all text content clickable
        function makeTextContentClickable(cell, handleEdit) {
            // Add click event to all existing text elements within the cell
            const textElements = cell.querySelectorAll('span, .error-text-hover');
            textElements.forEach(function(textElement) {
                // Remove any existing click listeners to prevent duplicates
                textElement.removeEventListener('click', handleEdit);
                textElement.addEventListener('click', handleEdit);
                // Make text elements visually clickable
                textElement.style.cursor = 'pointer';
                textElement.classList.add('clickable-text');
            });

            // Wrap any direct text nodes in clickable spans
            const childNodes = Array.from(cell.childNodes);
            childNodes.forEach(function(node) {
                if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
                    // Wrap text nodes in spans to make them clickable
                    const span = document.createElement('span');
                    span.textContent = node.textContent;
                    span.style.cursor = 'pointer';
                    span.classList.add('clickable-text');
                    span.addEventListener('click', handleEdit);
                    cell.replaceChild(span, node);
                }
            });
        }

        // Function to open medication external dropdown
        function openMedicationInlineDropdown(cell, rowIndex, columnIndex, currentValue) {
            if (cell.classList.contains('editing')) return;

            // Mark cell as editing to prevent multiple dropdowns
            cell.classList.add('editing');

            // Get cell position for dropdown placement
            const cellRect = cell.getBoundingClientRect();

            // Create dropdown container
            const dropdownContainer = document.createElement('div');
            dropdownContainer.className = 'medication-external-dropdown';
            dropdownContainer.style.cssText = `
                position: fixed;
                background: transparent !important;
                z-index: 9999 !important;
                min-width: 250px;
                padding: 0 !important;
                border: none !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            `;

            // Create select element
            const select = document.createElement('select');
            select.className = 'form-control medication-external-select';
            select.id = 'medication-select-' + Date.now();

            // Add medication options from the medications data
            <?php if(isset($medications)): ?>
                <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    const option<?php echo e($loop->index); ?> = document.createElement('option');
                    option<?php echo e($loop->index); ?>.value = '<?php echo e($medication->id); ?>';
                    option<?php echo e($loop->index); ?>.textContent = '<?php echo e($medication->name); ?>';
                    // Only pre-select if current value exactly matches a valid option
                    if ('<?php echo e($medication->name); ?>' === currentValue.trim()) {
                        option<?php echo e($loop->index); ?>.selected = true;
                    }
                    select.appendChild(option<?php echo e($loop->index); ?>);
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            // Add select to container
            dropdownContainer.appendChild(select);

            // Add container to body
            document.body.appendChild(dropdownContainer);

            // Position dropdown near the cell
            let left = cellRect.left;
            let top = cellRect.bottom + 5;

            // Adjust position if dropdown would go off screen
            const dropdownRect = dropdownContainer.getBoundingClientRect();
            if (left + dropdownRect.width > window.innerWidth) {
                left = window.innerWidth - dropdownRect.width - 10;
            }
            if (top + dropdownRect.height > window.innerHeight) {
                top = cellRect.top - dropdownRect.height - 5;
            }

            dropdownContainer.style.left = left + 'px';
            dropdownContainer.style.top = top + 'px';

            // Initialize Select2 with search functionality
            $(select).select2({
                width: '100%',
                placeholder: 'Search medications...',
                allowClear: false,
                dropdownParent: $(dropdownContainer)
            });

            // Clear any invalid pre-selection by checking if current value matches any valid option
            const validMedicationOptions = Array.from(select.options).map(option => option.textContent);
            if (!validMedicationOptions.includes(currentValue.trim())) {
                $(select).val(null).trigger('change');
            }

            // Open the dropdown
            $(select).select2('open');

            // Handle save on change
            const saveEdit = function() {
                const selectedOption = $(select).find('option:selected');
                const newValue = selectedOption.length ? selectedOption.text() : '';

                // Destroy Select2 and remove dropdown
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');

                // Only update if the value actually changed
                if (newValue !== currentValue && newValue !== '') {
                    updateCellValue(cell, rowIndex, columnIndex, newValue, currentValue);
                } else {
                    // Re-initialize editing and hover events
                    initializeInlineEditing();
                    initializeErrorTextHover();
                }
            };

            // Handle cancel
            const cancelEdit = function() {
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');
                // Re-initialize editing and hover events
                initializeInlineEditing();
                initializeErrorTextHover();
            };

            // Event listeners
            $(select).on('select2:select', saveEdit);
            $(select).on('select2:close', function() {
                setTimeout(cancelEdit, 100); // Small delay to allow selection to process
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                if (!dropdownContainer.contains(e.target) && e.target !== cell) {
                    document.removeEventListener('click', closeDropdown);
                    cancelEdit();
                }
            });
        }

        // Function to open ship to external dropdown
        function openShipToInlineDropdown(cell, rowIndex, columnIndex, currentValue) {
            if (cell.classList.contains('editing')) return;

            // Mark cell as editing to prevent multiple dropdowns
            cell.classList.add('editing');

            // Get cell position for dropdown placement
            const cellRect = cell.getBoundingClientRect();

            // Create dropdown container
            const dropdownContainer = document.createElement('div');
            dropdownContainer.className = 'ship-to-external-dropdown';
            dropdownContainer.style.cssText = `
                position: fixed;
                background: transparent !important;
                z-index: 9999 !important;
                min-width: 250px;
                padding: 0 !important;
                border: none !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            `;

            // Create select element
            const select = document.createElement('select');
            select.className = 'form-control ship-to-external-select';
            select.id = 'ship-to-select-' + Date.now();

            // Add ship to options from the shipToOptions data
            <?php if(isset($shipToOptions)): ?>
                <?php $__currentLoopData = $shipToOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shipToOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    const option<?php echo e($loop->index); ?> = document.createElement('option');
                    option<?php echo e($loop->index); ?>.value = '<?php echo e($shipToOption->id); ?>';
                    option<?php echo e($loop->index); ?>.textContent = '<?php echo e($shipToOption->name); ?>';
                    // Only pre-select if current value exactly matches a valid option
                    if ('<?php echo e($shipToOption->name); ?>' === currentValue.trim()) {
                        option<?php echo e($loop->index); ?>.selected = true;
                    }
                    select.appendChild(option<?php echo e($loop->index); ?>);
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            // Add select to container
            dropdownContainer.appendChild(select);

            // Add container to body
            document.body.appendChild(dropdownContainer);

            // Position dropdown near the cell
            let left = cellRect.left;
            let top = cellRect.bottom + 5;

            // Adjust position if dropdown would go off screen
            const dropdownRect = dropdownContainer.getBoundingClientRect();
            if (left + dropdownRect.width > window.innerWidth) {
                left = window.innerWidth - dropdownRect.width - 10;
            }
            if (top + dropdownRect.height > window.innerHeight) {
                top = cellRect.top - dropdownRect.height - 5;
            }

            dropdownContainer.style.left = left + 'px';
            dropdownContainer.style.top = top + 'px';

            // Initialize Select2 with search functionality
            $(select).select2({
                width: '100%',
                placeholder: 'Search ship to options...',
                allowClear: false,
                dropdownParent: $(dropdownContainer)
            });

            // Clear any invalid pre-selection by checking if current value matches any valid option
            const validOptions = Array.from(select.options).map(option => option.textContent);
            if (!validOptions.includes(currentValue.trim())) {
                $(select).val(null).trigger('change');
            }

            // Open the dropdown
            $(select).select2('open');

            // Handle save on change
            const saveEdit = function() {
                const selectedOption = $(select).find('option:selected');
                const newValue = selectedOption.length ? selectedOption.text() : '';

                // Destroy Select2 and remove dropdown
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');

                // Only update if the value actually changed
                if (newValue !== currentValue && newValue !== '') {
                    updateCellValue(cell, rowIndex, columnIndex, newValue, currentValue);
                } else {
                    // Re-initialize editing and hover events
                    initializeInlineEditing();
                    initializeErrorTextHover();
                }
            };

            // Handle cancel
            const cancelEdit = function() {
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');
                // Re-initialize editing and hover events
                initializeInlineEditing();
                initializeErrorTextHover();
            };

            // Event listeners
            $(select).on('select2:select', saveEdit);
            $(select).on('select2:close', function() {
                setTimeout(cancelEdit, 100); // Small delay to allow selection to process
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                if (!dropdownContainer.contains(e.target) && e.target !== cell) {
                    document.removeEventListener('click', closeDropdown);
                    cancelEdit();
                }
            });
        }

        // Function to open gender external dropdown
        function openGenderInlineDropdown(cell, rowIndex, columnIndex, currentValue) {
            if (cell.classList.contains('editing')) return;

            // Mark cell as editing to prevent multiple dropdowns
            cell.classList.add('editing');

            // Get cell position for dropdown placement
            const cellRect = cell.getBoundingClientRect();

            // Create dropdown container
            const dropdownContainer = document.createElement('div');
            dropdownContainer.className = 'gender-external-dropdown';
            dropdownContainer.style.cssText = `
                position: fixed;
                background: transparent !important;
                z-index: 9999 !important;
                min-width: 200px;
                padding: 0 !important;
                border: none !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            `;

            // Create select element
            const select = document.createElement('select');
            select.className = 'form-control gender-external-select';
            select.id = 'gender-select-' + Date.now();

            // Add gender options from the genders data
            <?php if(isset($genders)): ?>
                <?php $__currentLoopData = $genders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gender): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    const option<?php echo e($loop->index); ?> = document.createElement('option');
                    option<?php echo e($loop->index); ?>.value = '<?php echo e($gender->short_name); ?>';
                    option<?php echo e($loop->index); ?>.textContent = '<?php echo e($gender->name); ?>';
                    // Only pre-select if current value exactly matches a valid option
                    if ('<?php echo e($gender->short_name); ?>' === currentValue.trim()) {
                        option<?php echo e($loop->index); ?>.selected = true;
                    }
                    select.appendChild(option<?php echo e($loop->index); ?>);
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            // Add select to container
            dropdownContainer.appendChild(select);

            // Add container to body
            document.body.appendChild(dropdownContainer);

            // Position dropdown near the cell
            let left = cellRect.left;
            let top = cellRect.bottom + 5;

            // Adjust position if dropdown would go off screen
            const dropdownRect = dropdownContainer.getBoundingClientRect();
            if (left + dropdownRect.width > window.innerWidth) {
                left = window.innerWidth - dropdownRect.width - 10;
            }
            if (top + dropdownRect.height > window.innerHeight) {
                top = cellRect.top - dropdownRect.height - 5;
            }

            dropdownContainer.style.left = left + 'px';
            dropdownContainer.style.top = top + 'px';

            // Initialize Select2 with search functionality
            $(select).select2({
                width: '100%',
                placeholder: 'Search gender...',
                allowClear: false,
                dropdownParent: $(dropdownContainer)
            });

            // Clear any invalid pre-selection by checking if current value matches any valid option
            const validGenderOptions = Array.from(select.options).map(option => option.value);
            if (!validGenderOptions.includes(currentValue.trim())) {
                $(select).val(null).trigger('change');
            }

            // Open the dropdown
            $(select).select2('open');

            // Handle save on change
            const saveEdit = function() {
                const selectedOption = $(select).find('option:selected');
                const newValue = selectedOption.length ? selectedOption.val() : ''; // Use value (short_name) for saving

                // Destroy Select2 and remove dropdown
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');

                // Only update if the value actually changed
                if (newValue !== currentValue && newValue !== '') {
                    updateCellValue(cell, rowIndex, columnIndex, newValue, currentValue);
                } else {
                    // Re-initialize editing and hover events
                    initializeInlineEditing();
                    initializeErrorTextHover();
                }
            };

            // Handle cancel
            const cancelEdit = function() {
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');
                // Re-initialize editing and hover events
                initializeInlineEditing();
                initializeErrorTextHover();
            };

            // Event listeners
            $(select).on('select2:select', saveEdit);
            $(select).on('select2:close', function() {
                setTimeout(cancelEdit, 100); // Small delay to allow selection to process
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                if (!dropdownContainer.contains(e.target) && e.target !== cell) {
                    document.removeEventListener('click', closeDropdown);
                    cancelEdit();
                }
            });
        }

        // Function to open state external dropdown
        function openStateInlineDropdown(cell, rowIndex, columnIndex, currentValue) {
            if (cell.classList.contains('editing')) return;

            // Mark cell as editing to prevent multiple dropdowns
            cell.classList.add('editing');

            // Get cell position for dropdown placement
            const cellRect = cell.getBoundingClientRect();

            // Create dropdown container
            const dropdownContainer = document.createElement('div');
            dropdownContainer.className = 'state-external-dropdown';
            dropdownContainer.style.cssText = `
                position: fixed;
                background: transparent !important;
                z-index: 9999 !important;
                min-width: 250px;
                padding: 0 !important;
                border: none !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            `;

            // Create select element
            const select = document.createElement('select');
            select.className = 'form-control state-external-select';
            select.id = 'state-select-' + Date.now();

            // Add state options from the states data
            <?php if(isset($states)): ?>
                <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    const option<?php echo e($loop->index); ?> = document.createElement('option');
                    option<?php echo e($loop->index); ?>.value = '<?php echo e($state->short_name); ?>';
                    option<?php echo e($loop->index); ?>.textContent = '<?php echo e($state->name); ?>';
                    // Only pre-select if current value exactly matches a valid option
                    if ('<?php echo e($state->short_name); ?>' === currentValue.trim()) {
                        option<?php echo e($loop->index); ?>.selected = true;
                    }
                    select.appendChild(option<?php echo e($loop->index); ?>);
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>

            // Add select to container
            dropdownContainer.appendChild(select);

            // Add container to body
            document.body.appendChild(dropdownContainer);

            // Position dropdown near the cell
            let left = cellRect.left;
            let top = cellRect.bottom + 5;

            // Adjust position if dropdown would go off screen
            const dropdownRect = dropdownContainer.getBoundingClientRect();
            if (left + dropdownRect.width > window.innerWidth) {
                left = window.innerWidth - dropdownRect.width - 10;
            }
            if (top + dropdownRect.height > window.innerHeight) {
                top = cellRect.top - dropdownRect.height - 5;
            }

            dropdownContainer.style.left = left + 'px';
            dropdownContainer.style.top = top + 'px';

            // Initialize Select2 with search functionality
            $(select).select2({
                width: '100%',
                placeholder: 'Search states...',
                allowClear: false,
                dropdownParent: $(dropdownContainer)
            });

            // Clear any invalid pre-selection by checking if current value matches any valid option
            const validStateOptions = Array.from(select.options).map(option => option.value);
            if (!validStateOptions.includes(currentValue.trim())) {
                $(select).val(null).trigger('change');
            }

            // Open the dropdown
            $(select).select2('open');

            // Handle save on change
            const saveEdit = function() {
                const selectedOption = $(select).find('option:selected');
                const newValue = selectedOption.length ? selectedOption.val() : ''; // Use value (short_name) for saving

                // Destroy Select2 and remove dropdown
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');

                // Only update if the value actually changed
                if (newValue !== currentValue && newValue !== '') {
                    updateCellValue(cell, rowIndex, columnIndex, newValue, currentValue);
                } else {
                    // Re-initialize editing and hover events
                    initializeInlineEditing();
                    initializeErrorTextHover();
                }
            };

            // Handle cancel
            const cancelEdit = function() {
                $(select).select2('destroy');
                if (document.body.contains(dropdownContainer)) {
                    document.body.removeChild(dropdownContainer);
                }
                cell.classList.remove('editing');
                // Re-initialize editing and hover events
                initializeInlineEditing();
                initializeErrorTextHover();
            };

            // Event listeners
            $(select).on('select2:select', saveEdit);
            $(select).on('select2:close', function() {
                setTimeout(cancelEdit, 100); // Small delay to allow selection to process
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function closeDropdown(e) {
                if (!dropdownContainer.contains(e.target) && e.target !== cell) {
                    document.removeEventListener('click', closeDropdown);
                    cancelEdit();
                }
            });
        }

        // Function to create inline date picker using Flatpickr
        function createInlineDatePicker(cell, rowIndex, columnIndex, currentValue, restrictFutureDates = false) {
            if (cell.classList.contains('editing')) return;

            // Mark cell as editing to prevent multiple date pickers
            cell.classList.add('editing');

            // Store original content
            const originalContent = cell.innerHTML;

            // Parse current date value
            let dateValue = null;
            if (currentValue && currentValue.trim() !== '' && currentValue !== 'empty') {
                // Try to parse MM/DD/YYYY format
                const dateParts = currentValue.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/);
                if (dateParts) {
                    const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed in Date
                    const day = parseInt(dateParts[2]);
                    const year = parseInt(dateParts[3]);
                    dateValue = new Date(year, month, day);
                }
            }

            // Create date input
            const dateInput = document.createElement('input');
            dateInput.type = 'text';
            dateInput.style.cssText = `
                border: none !important;
                outline: none !important;
                background: transparent !important;
                padding: 0 !important;
                margin: 0 !important;
                width: 100% !important;
                height: 100% !important;
                box-sizing: border-box !important;
                font-size: inherit !important;
                font-family: inherit !important;
                line-height: inherit !important;
            `;

            // Replace cell content with date input
            cell.innerHTML = '';
            cell.appendChild(dateInput);

            // Configure Flatpickr options
            const flatpickrOptions = {
                dateFormat: "m/d/Y",
                defaultDate: dateValue,
                allowInput: true,
                clickOpens: true,
                onReady: function(selectedDates, dateStr, instance) {
                    // Set default date if no current value exists
                    if (!dateValue) {
                        if (restrictFutureDates) {
                            // DOB field - default to 1/1/1980
                            instance.setDate("1/1/1980", false);
                        } else {
                            // Script Date field - default to today
                            instance.setDate(new Date(), false);
                        }
                    }
                    // Open the calendar immediately
                    instance.open();
                },
                onClose: function(selectedDates, dateStr, instance) {
                    saveEdit(dateStr);
                }
            };

            // Set max date for DOB (no future dates)
            if (restrictFutureDates) {
                flatpickrOptions.maxDate = "today";
            }

            // Initialize Flatpickr
            const flatpickrInstance = flatpickr(dateInput, flatpickrOptions);

            // Handle save
            const saveEdit = function(selectedDateStr) {
                let newValue = selectedDateStr || '';

                // Destroy Flatpickr instance
                if (flatpickrInstance) {
                    flatpickrInstance.destroy();
                }

                cell.classList.remove('editing');

                // Only update if the value actually changed
                if (newValue !== currentValue) {
                    updateCellValue(cell, rowIndex, columnIndex, newValue, currentValue);
                } else {
                    // Restore original content
                    cell.innerHTML = originalContent;
                    // Re-initialize editing and hover events
                    initializeInlineEditing();
                    initializeErrorTextHover();
                }
            };

            // Handle cancel
            const cancelEdit = function() {
                // Destroy Flatpickr instance
                if (flatpickrInstance) {
                    flatpickrInstance.destroy();
                }

                cell.innerHTML = originalContent;
                cell.classList.remove('editing');
                // Re-initialize editing and hover events
                initializeInlineEditing();
                initializeErrorTextHover();
            };

            // Handle escape key
            dateInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });

            // Focus the input
            dateInput.focus();
        }

        // Function to open Script Date popup
        function openScriptDatePopup(cell, rowIndex, columnIndex, currentValue) {
            // Set current values in the modal
            $('#script-date-popup-modal').data('cell', cell);
            $('#script-date-popup-modal').data('rowIndex', rowIndex);
            $('#script-date-popup-modal').data('columnIndex', columnIndex);
            $('#script-date-popup-modal').data('originalValue', currentValue);

            // Set current date value in the input (Flatpickr expects MM/DD/YYYY format)
            let dateValue = '';
            if (currentValue && currentValue.trim() !== '' && currentValue !== 'empty') {
                dateValue = currentValue; // Already in MM/DD/YYYY format
            }

            // Set the value using Flatpickr's setDate method
            const scriptDatePicker = document.getElementById('script_date_input')._flatpickr;
            if (scriptDatePicker) {
                if (dateValue) {
                    scriptDatePicker.setDate(dateValue, false);
                } else {
                    scriptDatePicker.clear();
                }
            }

            // Show the modal
            $('#script-date-popup-modal').modal('show');
        }

        // Function to save Script Date selection
        function saveScriptDateSelection() {
            const modal = $('#script-date-popup-modal');
            const cell = modal.data('cell');
            const rowIndex = modal.data('rowIndex');
            const columnIndex = modal.data('columnIndex');
            const originalValue = modal.data('originalValue');

            // Get the selected date from Flatpickr (already in MM/DD/YYYY format)
            const selectedDate = $('#script_date_input').val();
            const newValue = selectedDate || '';

            if (newValue !== originalValue) {
                updateCellValue(cell, rowIndex, columnIndex, newValue, originalValue);
            }

            // Close the modal
            modal.modal('hide');
        }

        // Function to open DOB popup
        function openDOBPopup(cell, rowIndex, columnIndex, currentValue) {
            // Set current values in the modal
            $('#dob-popup-modal').data('cell', cell);
            $('#dob-popup-modal').data('rowIndex', rowIndex);
            $('#dob-popup-modal').data('columnIndex', columnIndex);
            $('#dob-popup-modal').data('originalValue', currentValue);

            // Set current date value in the input (Flatpickr expects MM/DD/YYYY format)
            let dateValue = '';
            if (currentValue && currentValue.trim() !== '' && currentValue !== 'empty') {
                dateValue = currentValue; // Already in MM/DD/YYYY format
            }

            // Set the value using Flatpickr's setDate method
            const dobDatePicker = document.getElementById('dob_date_input')._flatpickr;
            if (dobDatePicker) {
                if (dateValue) {
                    dobDatePicker.setDate(dateValue, false);
                } else {
                    dobDatePicker.clear();
                }
            }

            // Show the modal
            $('#dob-popup-modal').modal('show');
        }

        // Function to save DOB selection
        function saveDOBSelection() {
            const modal = $('#dob-popup-modal');
            const cell = modal.data('cell');
            const rowIndex = modal.data('rowIndex');
            const columnIndex = modal.data('columnIndex');
            const originalValue = modal.data('originalValue');

            // Get the selected date from Flatpickr (already in MM/DD/YYYY format)
            const selectedDate = $('#dob_date_input').val();
            const newValue = selectedDate || '';

            if (newValue !== originalValue) {
                updateCellValue(cell, rowIndex, columnIndex, newValue, originalValue);
            }

            // Close the modal
            modal.modal('hide');
        }



        function updateCellValue(cell, rowIndex, columnIndex, newValue, originalValue) {
            // Show loading state with a more visible spinner
            cell.innerHTML =
                '<div class="d-flex justify-content-center align-items-center" style="height: 20px;"><div class="spinner-border text-primary" style="width: 1.2rem; height: 1.2rem; border-width: 0.15em;" role="status"><span class="sr-only">Loading...</span></div></div>';
            cell.classList.remove('editing');
            // Remove dimension locks
            cell.style.removeProperty('--cell-width');
            cell.style.removeProperty('--cell-height');

            // Send AJAX request - use the dynamic route based on import type
            const updateRoute = '<?php echo e($updateRoute); ?>';
            fetch(updateRoute, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        row_index: parseInt(rowIndex),
                        column_index: parseInt(columnIndex),
                        value: newValue
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Check if this specific cell has an error
                        const cellHasError = data.cell_errors && data.cell_errors[columnIndex];

                        // Update cell content with appropriate styling
                        let cellContent = newValue || '<span class="text-muted">empty</span>';

                        // Handle date formatting for Script Date and DOB
                        if ((columnIndex == 0 || columnIndex == 3) && newValue && !isNaN(newValue)) {
                            cellContent = new Date((newValue - 25569) * 86400 * 1000).toLocaleDateString('en-US');
                        }

                        // Handle state column display - show short name in cell
                        if (columnIndex == 7 && newValue) {
                            // For state column, the newValue should already be the short_name from the dropdown
                            cellContent = newValue;
                        }

                        // Apply red styling if this cell has an error
                        if (cellHasError) {
                            const errorMessage = data.cell_errors[columnIndex];
                            // Check if the content is "empty" - if so, don't add hover functionality
                            if (newValue === '' || newValue === null) {
                                cell.innerHTML = `<span class="text-danger font-weight-bold">empty</span>`;
                            } else {
                                cell.innerHTML = `<span class="text-danger font-weight-bold error-text-hover"
                                                        data-error="${errorMessage}"
                                                        style="cursor: help;">${cellContent}</span>`;
                            }
                        } else {
                            cell.innerHTML = cellContent;
                        }

                        // Update cell styling based on validation
                        const row = cell.closest('tr');
                        const hasError = !data.is_valid;

                        // Update row error class
                        if (hasError) {
                            row.classList.add('error-row');
                        } else {
                            row.classList.remove('error-row');
                        }

                        // Update individual cell errors
                        updateCellErrors(row, data.cell_errors || {});

                        // Update statistics
                        updateStatistics(data.statistics);

                        // Re-initialize editing functionality for the updated cell
                        initializeInlineEditing();

                        // Show success feedback
                        showFeedback('Cell updated successfully', 'success');
                    } else {
                        // Revert to original value on error
                        cell.innerHTML = originalValue;
                        // Remove dimension locks on error
                        cell.style.removeProperty('--cell-width');
                        cell.style.removeProperty('--cell-height');
                        showFeedback(data.message || 'Failed to update cell', 'error');
                        // Re-initialize editing and hover events in case the reverted content has error styling
                        initializeInlineEditing();
                        initializeErrorTextHover();
                    }
                })
                .catch(error => {
                    console.error('Error updating cell:', error);
                    cell.innerHTML = originalValue;
                    // Remove dimension locks on network error
                    cell.style.removeProperty('--cell-width');
                    cell.style.removeProperty('--cell-height');
                    showFeedback('Network error occurred', 'error');
                    // Re-initialize editing and hover events in case the reverted content has error styling
                    initializeInlineEditing();
                    initializeErrorTextHover();
                });
        }

        function updateCellErrors(row, cellErrors) {
            // Update error styling for cells
            row.querySelectorAll('td').forEach(function(cell, index) {
                cell.classList.remove('has-error');

                // Skip the first column (row number column) - only process data columns
                if (index > 0) {
                    const dataColumnIndex = index - 1; // Adjust for row number column

                    // Check if this data column has an error
                    if (cellErrors[dataColumnIndex]) {
                        cell.classList.add('has-error');

                        // Update the span inside the cell to have error styling
                        const span = cell.querySelector('span');
                        if (span) {
                            span.classList.add('text-danger', 'font-weight-bold');
                            // Only add hover functionality if it's not "empty" text
                            if (span.textContent.trim() !== 'empty') {
                                span.classList.add('error-text-hover');
                                span.setAttribute('data-error', cellErrors[dataColumnIndex]);
                                span.style.cursor = 'help';
                            }
                        }
                    } else {
                        // Remove error styling if no error
                        const span = cell.querySelector('span');
                        if (span) {
                            span.classList.remove('text-danger', 'font-weight-bold', 'error-text-hover');
                            span.removeAttribute('data-error');
                            span.style.cursor = '';
                        }
                    }
                }
            });

            // Reinitialize both editing and error text hover events
            initializeInlineEditing();
            initializeErrorTextHover();
        }

        function updateStatistics(stats) {
            // Update the statistics display
            const alertElement = document.querySelector('.alert');
            if (alertElement && stats) {
                let errorMessage = '';
                if (stats.error_count > 0 && stats.error_rows && stats.error_rows.length > 0) {
                    const clickableRowNumbers = stats.error_rows.map(rowNumber =>
                        `<span class="error-row-link" onclick="scrollToRow(${rowNumber})" style="color: red; cursor: pointer; text-decoration: underline;">${rowNumber}</span>`
                    ).join(',');
                    errorMessage =
                        `, <span style="color: red;">Row number</span> ${clickableRowNumbers} <span style="color: red;">has an error</span>`;
                }

                alertElement.innerHTML = `
                <strong>${stats.valid_rows} rows</strong> read successfully,
                <strong class="text-danger">${stats.error_count} rows</strong>
                <span style="color: red"> with errors</span>${errorMessage}
            `;
            }

            // Update continue button state
            const continueBtn = document.getElementById('continue-btn');
            const errorMessage = document.querySelector('.text-danger.mt-2');

            if (continueBtn) {
                if (stats.valid_rows <= 0) {
                    continueBtn.disabled = true;
                    if (errorMessage) {
                        errorMessage.style.display = 'block';
                    }
                } else {
                    continueBtn.disabled = false;
                    if (errorMessage) {
                        errorMessage.style.display = 'none';
                    }
                }
            }
        }

        function initializeErrorTextHover() {
            // Clean up any existing tooltips first
            hideAllTooltips();

            // Remove existing event listeners to prevent duplicates
            document.querySelectorAll('.error-text-hover').forEach(function(element) {
                element.removeEventListener('mouseenter', showErrorTooltip);
                element.removeEventListener('mouseleave', hideErrorTooltip);
                element.removeEventListener('click', preventClickPropagation);
            });

            // Add hover events to all error text elements
            document.querySelectorAll('.error-text-hover').forEach(function(element) {
                element.addEventListener('mouseenter', showErrorTooltip);
                element.addEventListener('mouseleave', hideErrorTooltip);
                element.addEventListener('click', preventClickPropagation);
            });
        }

        function preventClickPropagation(event) {
            event.preventDefault();
            event.stopPropagation();
            // Force hide tooltip on click
            hideErrorTooltip(event);
        }

        function showErrorTooltip(event) {
            const btn = event.target;
            const errorMessage = btn.getAttribute('data-error');

            // Hide any existing tooltip for this button first
            if (btn._tooltip) {
                try {
                    btn._tooltip.remove();
                } catch (e) {
                    // Ignore errors
                }
                btn._tooltip = null;
            }

            // Hide all other tooltips
            hideAllTooltips();

            // Create tooltip element
            const tooltip = document.createElement('div');
            tooltip.className = 'error-tooltip';
            tooltip.textContent = 'Error: ' + errorMessage;
            tooltip.style.cssText = `
                position: fixed;
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                white-space: normal;
                z-index: 1000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                pointer-events: none;
                max-width: 300px;
                word-wrap: break-word;
                overflow-wrap: break-word;
                hyphens: auto;
                line-height: 1.4;
            `;

            document.body.appendChild(tooltip);

            // Position tooltip with edge detection using fixed positioning
            const rect = btn.getBoundingClientRect();
            const tooltipWidth = tooltip.offsetWidth;
            const tooltipHeight = tooltip.offsetHeight;
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
            const scrollY = window.pageYOffset || document.documentElement.scrollTop;

            // Calculate initial position (centered above button)
            let left = rect.left + rect.width / 2 - tooltipWidth / 2;
            let top = rect.top - tooltipHeight - 8;

            // Check if tooltip goes off the right edge
            if (left + tooltipWidth > windowWidth - 10) {
                // Position to the left of the button
                left = rect.left - tooltipWidth - 8;
            }

            // Check if tooltip goes off the left edge
            if (left < 10) {
                // Position to the right of the button
                left = rect.right + 8;
            }

            // Check if tooltip goes off the top edge
            if (top < 10) {
                // Position below the button instead
                top = rect.bottom + 8;
            }

            // Check if tooltip goes off the bottom edge
            if (top + tooltipHeight > windowHeight - 10) {
                // Position above the button
                top = rect.top - tooltipHeight - 8;
            }

            // Ensure tooltip doesn't go off screen edges
            left = Math.max(10, Math.min(left, windowWidth - tooltipWidth - 10));
            top = Math.max(10, Math.min(top, windowHeight - tooltipHeight - 10));

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';

            // Store reference for cleanup
            btn._tooltip = tooltip;

            // Auto-hide tooltip after 10 seconds as a failsafe
            setTimeout(function() {
                if (btn._tooltip === tooltip) {
                    try {
                        tooltip.remove();
                    } catch (e) {
                        // Ignore errors
                    }
                    btn._tooltip = null;
                }
            }, 10000);
        }

        function hideErrorTooltip(event) {
            const btn = event.target;
            if (btn && btn._tooltip) {
                try {
                    btn._tooltip.remove();
                } catch (e) {
                    // Ignore errors if tooltip already removed
                }
                btn._tooltip = null;
            }
        }

        function hideAllTooltips() {
            // Method 1: Remove tooltips via element references
            document.querySelectorAll('.error-text-hover').forEach(function(element) {
                if (element._tooltip) {
                    try {
                        element._tooltip.remove();
                    } catch (e) {
                        // Ignore errors if tooltip already removed
                    }
                    element._tooltip = null;
                }
            });

            // Method 2: Remove any orphaned tooltips by class
            document.querySelectorAll('.error-tooltip').forEach(function(tooltip) {
                try {
                    tooltip.remove();
                } catch (e) {
                    // Ignore errors if tooltip already removed
                }
            });
        }

        // Multiple event listeners to ensure tooltips are always hidden
        document.addEventListener('click', hideAllTooltips);
        document.addEventListener('scroll', hideAllTooltips);
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                hideAllTooltips();
            }
        });

        // Hide tooltips when window loses focus
        window.addEventListener('blur', hideAllTooltips);

        // Hide tooltips on window resize
        window.addEventListener('resize', hideAllTooltips);

        function showFeedback(message, type) {
            // Create or update feedback element
            let feedback = document.getElementById('edit-feedback');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.id = 'edit-feedback';
                feedback.style.position = 'fixed';
                feedback.style.top = '20px';
                feedback.style.right = '20px';
                feedback.style.zIndex = '9999';
                feedback.style.padding = '10px 15px';
                feedback.style.borderRadius = '4px';
                feedback.style.fontSize = '14px';
                feedback.style.fontWeight = 'bold';
                document.body.appendChild(feedback);
            }

            // Set message and styling
            feedback.textContent = message;
            feedback.className = type === 'success' ? 'alert alert-success' : 'alert alert-danger';
            feedback.style.display = 'block';

            // Auto-hide after 3 seconds
            setTimeout(function() {
                feedback.style.display = 'none';
            }, 3000);
        }

        function scrollToRow(rowNumber) {
            const targetRow = document.getElementById('row-' + rowNumber);
            if (targetRow) {
                // Scroll to the row with smooth behavior
                targetRow.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Add a temporary highlight effect
                targetRow.style.transition = 'background-color 0.3s ease';
                const originalBackground = targetRow.style.backgroundColor;
                targetRow.style.backgroundColor = '#ffeb3b'; // Yellow highlight

                // Remove highlight after 2 seconds
                setTimeout(function() {
                    targetRow.style.backgroundColor = originalBackground;
                }, 2000);
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card card-custom">
                    <div class="card-header">
                        <div class="card-title">
                            <h3 class="card-label"><?php echo e($pageTitle ?? 'Data Preview'); ?></h3>
                        </div>
                    </div>

                    <div class="card-body">
                        <!-- Progress Steps -->
                        <?php if(isset($showProgressSteps) && $showProgressSteps): ?>
                            <div class="progress-steps-container mb-5">
                                <div class="progress-line">
                                    <div class="progress-line-inner"
                                        style="width: <?php echo e(($currentStep / $totalSteps) * 100); ?>%;">
                                    </div>
                                </div>
                                <div class="progress-steps">
                                    <div class="progress-step <?php echo e($currentStep >= 1 ? 'active' : ''); ?>">
                                        <div class="step-circle">1</div>
                                        <div class="step-label">Upload</div>
                                    </div>
                                    <div class="progress-step <?php echo e($currentStep >= 2 ? 'active' : ''); ?>">
                                        <div class="step-circle">2</div>
                                        <div class="step-label">Create</div>
                                    </div>
                                    <?php if($totalSteps >= 4): ?>
                                        <div class="progress-step <?php echo e($currentStep >= 3 ? 'active' : ''); ?>">
                                            <div class="step-circle">3</div>
                                            <div class="step-label">Sign</div>
                                        </div>
                                        <div class="progress-step <?php echo e($currentStep >= 4 ? 'active' : ''); ?>">
                                            <div class="step-circle">4</div>
                                            <div class="step-label">Done</div>
                                        </div>
                                    <?php else: ?>
                                        <div class="progress-step <?php echo e($currentStep >= 3 ? 'active' : ''); ?>">
                                            <div class="step-circle">3</div>
                                            <div class="step-label">Done</div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Selected Provider Info (for staff import) -->
                        <?php if(isset($selectedProvider)): ?>
                            <div class="mb-4">
                                <h6><i class="fas fa-user-md"></i> Selected Provider:
                                    <strong><?php echo e($selectedProvider->first_name); ?>

                                        <?php echo e($selectedProvider->last_name); ?></strong>
                                </h6>
                            </div>
                        <?php endif; ?>

                        <!-- Data Summary -->
                        <div class="alert">
                            <strong><?php echo e($validRows); ?> rows</strong> read successfully,
                            <strong class="text-danger"><?php echo e($errorCount); ?> rows</strong>
                            <span style="color: red"> with errors</span>
                            <?php if($errorCount > 0 && !empty($errorRows)): ?>
                                , <span style="color: red;">Row number</span>
                                <?php $__currentLoopData = $errorRows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $rowNumber): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="error-row-link" onclick="scrollToRow(<?php echo e($rowNumber); ?>)"
                                        style="color: red; cursor: pointer; text-decoration: underline;"><?php echo e($rowNumber); ?></span>
                                    <?php if($index < count($errorRows) - 1): ?>
                                        ,
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <span style="color: red;">has an error</span>
                            <?php endif; ?>
                        </div>

                        <!-- Data Preview Table -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover ">
                                <thead>
                                    <tr style="background-color: #000000; color: white;">
                                        <th>#</th>
                                        <?php $__currentLoopData = $columnHeaders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $columnName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($index < 17): ?>
                                                <th>
                                                    <?php echo e($columnName); ?>

                                                </th>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $allRowsData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            // Check if any cell in this row has an error
                                            $hasError =
                                                isset($cellErrors[$index + 2]) && !empty($cellErrors[$index + 2]);

                                            // Check if this row has any data
                                            $hasData = false;
                                            foreach ($row as $cellValue) {
                                                if ($cellValue !== null && $cellValue !== '') {
                                                    $hasData = true;
                                                    break;
                                                }
                                            }

                                            // Check if this is a blank row (should be marked as error)
                                            $isBlankRow = !$hasData;
                                        ?>
                                        <tr id="row-<?php echo e($index + 2); ?>"
                                            class="<?php echo e($hasError || $isBlankRow ? 'error-row' : ''); ?>">
                                            <td><?php echo e($index + 2); ?></td>

                                            <?php for($i = 0; $i < min(17, count($columnHeaders)); $i++): ?>
                                                <td class="<?php echo e(isset($cellErrors[$index + 2][$i]) || $isBlankRow ? 'has-error' : ''); ?> editable-cell"
                                                    data-row-index="<?php echo e($index); ?>"
                                                    data-column-index="<?php echo e($i); ?>"
                                                    style="cursor: pointer; position: relative;">
                                                    <?php if(isset($row[$i]) && $row[$i] !== null && $row[$i] !== ''): ?>
                                                        <span
                                                            class="<?php echo e(isset($cellErrors[$index + 2][$i]) ? 'text-danger font-weight-bold error-text-hover' : ''); ?>"
                                                            <?php if(isset($cellErrors[$index + 2][$i])): ?> data-error="<?php echo e($cellErrors[$index + 2][$i]); ?>"
                                                                style="cursor: help;" <?php endif; ?>>
                                                            <?php if($i == 0 || $i == 3): ?>
                                                                
                                                                <?php if(is_numeric($row[$i])): ?>
                                                                    <?php echo e(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[$i])->format('m/d/Y')); ?>

                                                                <?php else: ?>
                                                                    <?php echo e($row[$i]); ?>

                                                                <?php endif; ?>
                                                            <?php else: ?>
                                                                <?php echo e($row[$i]); ?>

                                                            <?php endif; ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <?php if($isBlankRow && $i == 0): ?>
                                                            
                                                            <span class="text-danger font-weight-bold error-text-hover"
                                                                data-error="Blank row detected" style="cursor: help;">Blank
                                                                row</span>
                                                        <?php elseif($hasError || $isBlankRow): ?>
                                                            
                                                            <span class="text-danger font-weight-bold">empty</span>
                                                        <?php else: ?>
                                                            
                                                            <span class="text-muted">empty</span>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </td>
                                            <?php endfor; ?>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between mt-5">
                            <a href="<?php echo e($backRoute); ?>" class="btn btn-outline-secondary px-4 py-2 back-btn">
                                <i class="fas fa-arrow-left mr-1"></i> Back
                            </a>
                            <form action="<?php echo e($processRoute); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-danger px-4" <?php echo e($validRows <= 0 ? 'disabled' : ''); ?>

                                    id="continue-btn">
                                    <span class="continue-text">Continue <i class="fas fa-arrow-right ml-1"></i></span>
                                </button>
                                <?php if($validRows <= 0): ?>
                                    <div class="text-danger mt-2">
                                        <small>Cannot continue - all rows contain errors</small>
                                    </div>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script Date Selection Modal -->
    <div class="modal fade" id="script-date-popup-modal" tabindex="-1" role="dialog"
        aria-labelledby="scriptDateModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scriptDateModalLabel">Select Script Date</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="col-md-12 mb-3">
                        <label for="script_date_input">Script Date:</label>
                        <input type="text" class="form-control" id="script_date_input" placeholder="Select date...">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveScriptDateSelection()">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- DOB Selection Modal -->
    <div class="modal fade" id="dob-popup-modal" tabindex="-1" role="dialog" aria-labelledby="dobModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dobModalLabel">Select Date of Birth</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="col-md-12 mb-3">
                        <label for="dob_date_input">Date of Birth:</label>
                        <input type="text" class="form-control" id="dob_date_input" placeholder="Select date...">
                        <small class="form-text text-muted">Future dates are not allowed for date of birth.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveDOBSelection()">Save</button>
                </div>
            </div>
        </div>
    </div>





    <style>
        /* Custom alert styling */
        .alert {
            background-color: #dff9ff;
            color: #333;
            border: none;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .card {
            border: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            border-radius: 0;
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #f1f1f1;
            padding: 15px 20px;
        }

        /* Progress Steps Styling */
        .progress-steps-container {
            position: relative;
            padding: 20px 0;
            margin: 0 auto;
            max-width: 700px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .progress-step {
            text-align: center;
            width: 20%;
            position: relative;
        }

        .step-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .progress-step.active .step-circle {
            background-color: #000000;
            color: white;
        }

        .step-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 400;
        }

        .progress-step.active .step-label {
            color: #000000;
            font-weight: 500;
        }

        .progress-line {
            position: absolute;
            top: 38px;
            left: 10%;
            right: 10%;
            height: 2px;
            background-color: #e9ecef;
            z-index: 0;
        }

        .progress-line-inner {
            height: 100%;
            background-color: #000000;
        }

        .table.custom-striped tbody tr:not(.error-row):nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .table.custom-striped tbody tr:not(.error-row):hover {
            background-color: rgba(0, 0, 0, 0.1);
        }

        .btn-danger {
            background-color: #000000;
            border-color: #000000;
        }

        /* Cell error styling */
        .bg-danger {
            background-color: #dc3545 !important;
        }

        .bg-danger:hover {
            background-color: #bd2130 !important;
        }

        .small.text-white {
            font-size: 80%;
        }

        /* Error styling */
        .text-danger {
            color: #dc3545 !important;
            cursor: pointer;
        }

        /* Error row styling */
        .error-row {
            background-color: rgba(220, 53, 69, 0.1) !important;
        }

        .error-row:hover {
            background-color: rgba(220, 53, 69, 0.15) !important;
        }

        /* Cell with error styling */
        .has-error {
            position: relative;
        }

        /* Make blank row errors more visible */
        .text-danger[title*="Blank row"] {
            font-size: 1.1em;
            font-weight: bold !important;
            color: #dc3545 !important;
        }

        /* Ensure error text is clearly visible */
        .text-danger {
            color: #dc3545 !important;
            font-weight: bold !important;
        }

        /* Back button styling to match Continue button */
        .back-btn {
            font-size: 1rem;
            line-height: 1.5;
            border-width: 1px;
            height: 38px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* Spinner styling */
        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
            border-width: 0.15em;
            vertical-align: middle;
        }

        /* Standard spinner styling */
        .spinner-border {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            vertical-align: text-bottom;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border 0.75s linear infinite;
        }

        @keyframes spinner-border {
            to {
                transform: rotate(360deg);
            }
        }

        /* Ensure button maintains its size when showing spinner */
        button[type="submit"] {
            min-width: 120px;
        }

        /* Inline editing styles */
        .editable-cell {
            transition: background-color 0.2s ease;
            /* Prevent size changes during editing only */
            box-sizing: border-box;
            position: relative;
        }

        /* Make text content within cells clearly clickable */
        .editable-cell span,
        .editable-cell .error-text-hover,
        .editable-cell .clickable-text {
            cursor: pointer !important;
            transition: all 0.15s ease;
            padding: 1px 2px;
            border-radius: 2px;
            display: inline-block;
        }

        /* Enhanced hover effect for text elements */
        .editable-cell span:hover,
        .editable-cell .error-text-hover:hover,
        .editable-cell .clickable-text:hover {
            background-color: rgba(0, 123, 255, 0.1);
            transform: scale(1.02);
            box-shadow: 0 1px 3px rgba(0, 123, 255, 0.2);
        }

        /* Special styling for clickable text */
        .clickable-text {
            position: relative;
        }

        /* Add a subtle underline on hover for clickable text */
        .clickable-text:hover {
            text-decoration: underline;
            text-decoration-color: rgba(0, 123, 255, 0.5);
            text-underline-offset: 2px;
        }

        .editable-cell:hover {
            background-color: rgba(0, 123, 255, 0.1) !important;
        }

        .editable-cell:hover::after {
            content: "✎";
            position: absolute;
            top: 2px;
            right: 4px;
            font-size: 12px;
            color: #5c5d5e;
            opacity: 0.7;
        }

        .editable-cell.editing {
            background-color: #d4edda !important;
            /* Use outline instead of border to prevent size changes */
            outline: 2px solid #28a745 !important;
            outline-offset: -2px;
            /* Lock dimensions during editing only */
            width: var(--cell-width) !important;
            height: var(--cell-height) !important;
        }

        .editable-cell.editing:hover::after {
            display: none;
        }

        /* Style the input to match cell dimensions exactly */
        .editable-cell.editing input {
            border: none !important;
            outline: none !important;
            background: transparent !important;
            padding: 0 !important;
            margin: 0 !important;
            width: 100% !important;
            height: 100% !important;
            box-sizing: border-box !important;
            font-size: inherit !important;
            font-family: inherit !important;
            line-height: inherit !important;
        }

        /* External dropdown styling for medication column */




        /* Fixed table layout with static column widths */
        .table {
            table-layout: fixed !important;
            width: 100% !important;
        }

        /* Static column widths */
        .table td,
        .table th {
            padding: 0.3rem 0.5rem !important;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-word;
            vertical-align: top;
        }

        /* Row # */
        .table td:nth-child(1),
        .table th:nth-child(1) {
            width: 50px;
        }

        /* Script Date*/
        .table td:nth-child(2),
        .table th:nth-child(2) {
            width: 90px;
        }

        /* Last Name  */
        .table td:nth-child(3),
        .table th:nth-child(3) {
            width: 120px;
        }

        /* First Name */
        .table td:nth-child(4),
        .table th:nth-child(4) {
            width: 120px;
        }

        /* DOB  */
        .table td:nth-child(5),
        .table th:nth-child(5) {
            width: 90px;
        }

        /* Gender */
        .table td:nth-child(6),
        .table th:nth-child(6) {
            width: 60px;
        }

        /* Address */
        .table td:nth-child(7),
        .table th:nth-child(7) {
            width: 200px;
        }

        /* City */
        .table td:nth-child(8),
        .table th:nth-child(8) {
            width: 100px !important;
        }

        /* State */
        .table td:nth-child(9),
        .table th:nth-child(9) {
            width: 60px;
        }

        /* Zipcode */
        .table td:nth-child(10),
        .table th:nth-child(10) {
            width: 100px;
        }

        /* Phone */
        .table td:nth-child(11),
        .table th:nth-child(11) {
            width: 120px;
        }

        /* Medication */
        .table td:nth-child(12),
        .table th:nth-child(12) {
            width: 150px;
        }



        /* Refills */
        .table td:nth-child(13),
        .table th:nth-child(13) {
            width: 60px;
        }

        /* Vial Qty */
        .table td:nth-child(14),
        .table th:nth-child(14) {
            width: 70px;
        }

        /* Days Supply */
        .table td:nth-child(15),
        .table th:nth-child(15) {
            width: 80px;
        }

        /* SIG */
        .table td:nth-child(16),
        .table th:nth-child(16) {
            width: 200px !important;
        }

        /* Note */
        .table td:nth-child(17),
        .table th:nth-child(17) {
            width: 120px !important;
        }

        /* Ship To */
        .table td:nth-child(18),
        .table th:nth-child(18) {
            width: 100px !important;
        }

        /* Table container */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Feedback styling */
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        /* Error row link styling */
        .error-row-link {
            color: #dc3545 !important;
            cursor: pointer;
            text-decoration: underline;
            font-weight: bold;
            transition: color 0.2s ease;
        }

        .error-row-link:hover {
            color: #a71e2a !important;
            text-decoration: underline;
        }

        .select2-container .select2-selection--single {
            height: 38px;
            display: flex;
            align-items: center;
            padding: 6px 12px;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            height: 100%;
            display: flex;
            align-items: center;
            padding-left: 8px;
            /* optional, spacing from text */
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/excel-import/shared-preview.blade.php ENDPATH**/ ?>