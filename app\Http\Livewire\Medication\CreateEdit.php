<?php

namespace App\Http\Livewire\Medication;

use Livewire\Component;
use App\Models\Medication;

class CreateEdit extends Component
{
    public Medication $medication;

    public function mount(Medication $medication)
    {
        $this->medication = $medication;
    }

    public function rules()
    {
        return [
            'medication.name' => $this->medication->id ? 'required|string|max:255|unique:medications,name,' . $this->medication->id : 'required|string|max:255|unique:medications,name',
            'medication.ndc' => 'required|string|max:255',
            'medication.dispensepro_medication_name' => 'required|string|max:255',
            'medication.refills' => 'required|numeric',
            'medication.vial_quantity' => 'required|numeric',
            'medication.days_supply' => 'required|numeric',
            'medication.sig' => 'nullable|string|max:1024',
            'medication.notes' => 'nullable|string|max:1024',
        ];
    }


    public function update($propertyName)
    {
        $this->validateOnly($propertyName);
    }

    public function store()
    {
        $this->validate();

        if (!$this->medication->id) {
            // Save the medication to the database

            $this->medication->save();

            $message = __('messages.medication_created');
        } else {
            // Update the existing medication
            $this->medication->save();

            // Optionally, you can add a success message or redirect
            $message = __('messages.medication_updated');
        }

        session()->flash('success-message', $message);

        return redirect()->route('medications.index');
    }

    public function render()
    {
        return view('livewire.medication.create-edit');
    }
}
