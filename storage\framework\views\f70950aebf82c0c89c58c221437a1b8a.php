<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>Prescription</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
        }

        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #000;
            padding: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        .header-table td {
            vertical-align: top;
            width: 50%;
        }

        .patient-table {
            width: 100%;
            border: 1px solid #000;
            margin-bottom: 15px;
        }

        .patient-table td {
            padding: 5px;
        }

        .patient-left {
            width: 60%;
            border-right: 1px solid #000;
        }

        .prescription-table {
            width: 100%;
            border: 1px solid #000;
            margin-bottom: 15px;
        }

        .prescription-table td {
            padding: 5px;
        }

        .signature-line {
            border-top: 1px solid #000;
            width: 60%;
            margin-top: 50px;
            margin-bottom: 10px;
        }

        .bold {
            font-weight: bold;
        }

        .prescription-row {
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header Section -->
        <table class="header-table">
            <tr>
                <td style="vertical-align: top;">
                    <div><span class="bold">DEA#</span> <?php echo e($user->{'DEA#'} ?? ''); ?></div>
                    <div><span class="bold">LIC#</span> <?php echo e($user->{'LIC#'} ?? ''); ?></div>
                    <div><span class="bold">NPI#</span> <?php echo e($user->{'NPI#'} ?? ''); ?></div>
                    <div><span class="bold"><?php echo e($user->clinic_name ?? ''); ?></span></div>
                    <div><span class="bold"><?php echo e($doctorName ?? ''); ?></span></div>
                    <div><?php echo e($user->address ?? ''); ?></div>
                    <div><?php echo e($user->city ?? ''); ?>, <?php echo e($userState->short_name ?? ''); ?> <?php echo e($user->zip ?? ''); ?>

                    </div>
                    <div><span class="bold">Phone:</span> <?php echo e($user->phone ?? ''); ?></div>
                    <div><span class="bold">Fax:</span> <?php echo e($user->fax ?? ''); ?></div>
                </td>
                <td style="vertical-align: bottom; padding-top: 30px; text-align: left;">
                    <div><span class="bold">DiRx Inc</span></div>
                    <div>3540 NW 56th St. Suite 204</div>
                    <div>Ft Lauderdale, FL 33309</div>
                    <div><span class="bold">Phone:</span> **********</div>
                    <div><span class="bold">Fax:</span> **********</div>
                </td>
            </tr>
        </table>

        <!-- Patient Information Section -->
        <table class="patient-table" style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
            <!-- Patient Name and Date Row -->
            <tr>
                <td
                    style="width: 70%; border-top: 1px solid #000; border-left: 1px solid #000; border-bottom: 1px solid #000; padding: 5px; word-break: break-word; white-space: normal;">
                    <div style="word-break: break-word; white-space: normal;"><span class="bold">Patient Name:</span> <?php echo e($data[2] . ' ' . $data[1]); ?></div>
                </td>
                <td
                    style="width: 30%; border-top: 1px solid #000; border-right: 1px solid #000; border-bottom: 1px solid #000; padding: 5px; text-align: right;">
                    <?php if(isset($isSigned) && $isSigned): ?>
                        <?php
                            if (isset($data[0]) && is_numeric($data[0])) {
                                $scriptDate = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($data[0]);
                                $scriptDateFormatted = $scriptDate->format('m/d/Y');
                            } else {
                                $scriptDateFormatted = $data[0];
                            }
                        ?>
                        <div><span class="bold">Date:</span> <?php echo e($scriptDateFormatted); ?></div>
                    <?php else: ?>
                        <?php
                            $excel_serial = $data[0] ?? null;

                            if (is_numeric($excel_serial)) {
                                $date = \Carbon\Carbon::createFromDate(1900, 1, 1)
                                    ->addDays($excel_serial - 2)
                                    ->format('m/d/Y');
                            } elseif (is_string($excel_serial) && !empty($excel_serial)) {
                                // Handle string dates like "7/11/2025" from date picker updates
                                try {
                                    $date = \Carbon\Carbon::parse($excel_serial)->format('m/d/Y');
                                } catch (\Exception $e) {
                                    $date = $excel_serial; // Use as-is if parsing fails
                                }
                            } else {
                                $date = '';
                            }
                        ?>

                        <div><span class="bold">Date:</span> <?php echo e($date); ?></div>
                    <?php endif; ?>
                </td>
            </tr>
            <!-- Patient Details Row - Two Column Layout -->
            <tr>
                <td style="width: 50%; border: 1px solid #000; padding: 5px; vertical-align: top;">
                    <?php if(isset($isSigned) && $isSigned): ?>
                        <?php
                            if (isset($data[3])) {
                                if (is_numeric($data[3])) {
                                    // Handle Excel date format (numeric)
                                    $dobDate = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($data[3]);
                                    $dobFormatted = $dobDate->format('m/d/Y');
                                    $age = \Carbon\Carbon::parse($dobDate)->age;
                                } else {
                                    // Handle string date format (like "09/19/1971")
                                    $dobFormatted = $data[3];
                                    try {
                                        $age = \Carbon\Carbon::parse($dobFormatted)->age;
                                    } catch (\Exception $e) {
                                        $age = '';
                                    }
                                }
                            } else {
                                $dobFormatted = '';
                                $age = '';
                            }
                        ?>
                        <div><span class="bold">DOB:</span> <?php echo e($dobFormatted); ?></div>
                        <div><span class="bold">Age:</span> <?php echo e($age); ?></div>
                    <?php else: ?>
                        <?php
                            $excel_serial = $data[3] ?? null;

                            if (is_numeric($excel_serial)) {
                                $dob = \Carbon\Carbon::createFromDate(1900, 1, 1)->addDays($excel_serial - 2);
                                $dob_date = $dob->format('m/d/Y');
                                $age = $dob->age;
                            } elseif (is_string($excel_serial) && !empty($excel_serial)) {
                                // Handle string dates like "7/11/2025" from date picker updates
                                try {
                                    $dob = \Carbon\Carbon::parse($excel_serial);
                                    $dob_date = $dob->format('m/d/Y');
                                    $age = $dob->age;
                                } catch (\Exception $e) {
                                    $dob_date = $excel_serial; // Use as-is if parsing fails
                                    $age = '';
                                }
                            } else {
                                $dob_date = '';
                                $age = '';
                            }
                        ?>

                        <div><span class="bold">DOB:</span> <?php echo e($dob_date); ?></div>
                        <div><span class="bold">Age:</span> <?php echo e($age); ?></div>
                    <?php endif; ?>
                    <div><span class="bold">Sex:</span> <?php echo e($data[4]); ?></div>
                </td>
                <td style="width: 50%; border: 1px solid #000; padding: 5px; vertical-align: top;">
                    <div><?php echo e($data[5]); ?></div>
                    <div><?php echo e($data[6]); ?>, <?php echo e($data[7]); ?> <?php echo e($data[8]); ?></div>
                    <div><span class="bold">Phone:</span> <?php echo e($data[9]); ?></div>
                </td>
            </tr>
        </table>

        <!-- Prescription Section -->
        <table class="prescription-table">
            <!-- Drug Row -->
            <tr>
                <td>
                    <div class="prescription-row"><span class="bold">Drug:</span>
                        <?php echo e($data[10]); ?></div>
                </td>
            </tr>
            <!-- Separator Line -->
            <tr>
                <td style="border-top: 1px solid #000; height: 1px; padding: 0;"></td>
            </tr>
            <!-- SIG and other details -->
            <tr>
                <td>
                    <div class="prescription-row" style="word-break: break-word; white-space: normal;"><span class="bold">SIG:</span> <?php echo e($data[14] ?? ''); ?></div>
                    <div class="prescription-row"><span class="bold">Dispense:</span> <?php echo e($data[12] ?? '1'); ?></div>
                    <div class="prescription-row"><span class="bold">Units:</span> each</div>
                    <div class="prescription-row"><span class="bold">Dispense as Written:</span> Pharmacy authorized
                        to replace vial with similar medication based on availability and dispense syringes.</div>
                    <div class="prescription-row"><span class="bold">Refills:</span> <?php echo e($data[11] ?? '0'); ?></div>
                    <div class="prescription-row"><span class="bold">Days Supply:</span> <?php echo e($data[13] ?? '28'); ?></div>
                    <div class="prescription-row" style="word-break: break-word; white-space: normal;"><span class="bold">Notes:</span> <?php echo e($data[15] ?? ''); ?></div>
                    <div class="prescription-row"><span class="bold">Ship To:</span> <?php echo e($data[16] ?? ''); ?></div>
                </td>
            </tr>
        </table>

        <!-- Signature Section -->
        <div>
            <div><span class="bold">Electronically signed by:</span>
                <?php if(isset($isSigned) && $isSigned): ?>
                    <?php echo e($doctorName ?? ''); ?>

                <?php endif; ?>
            </div>
            <?php if(isset($userSignature) && $userSignature): ?>
                <div style="margin-top: 10px; margin-bottom: 10px;">
                    <img src="<?php echo e($userSignature); ?>" alt="Doctor's Signature"
                        style="max-width: 200px; max-height: 80px;">
                </div>
            <?php else: ?>
                <div class="signature-line"></div>
            <?php endif; ?>
            <div>Substitution permitted</div>

            <?php if(isset($isSigned) && $isSigned): ?>
                <div>Signed at: <?php echo e($signed_at ?? now()->format('m/d/Y h:i A')); ?></div>
                <div>IP Address: <?php echo e($ip_address ?? request()->ip()); ?></div>
            <?php endif; ?>
        </div>
    </div>

    <?php if(!isset($isPdfDownload) || !$isPdfDownload): ?>
        <!-- Download Button (only visible in browser, not in PDF) -->
        <div class="download-button" style="text-align: center; margin-top: 20px;">
            <a href="<?php echo e(route('excel.download-pdf')); ?>"
                style="display: inline-block; background-color: #000000; color: white; padding: 12px 24px; text-decoration: none; font-size: 16px; border-radius: 4px; font-weight: bold;">
                Download PDF
            </a>
        </div>
    <?php endif; ?>

    <style>
        @media print {
            .download-button {
                display: none !important;
            }
        }
    </style>
</body>

</html>
<?php /**PATH C:\PROJECTS\Max-life\rx-maxlife-panel\resources\views/pdf/prescription.blade.php ENDPATH**/ ?>