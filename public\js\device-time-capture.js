/**
 * Device Time Capture Script
 *
 * This script captures the client device's current date and time
 * and sends it to the server to be stored in the session.
 * This is used for recording accurate timestamps when signing prescriptions.
 */
document.addEventListener("DOMContentLoaded", function () {
    // Function to send device time to server
    function sendDeviceTime() {
        // Get the current date
        const now = new Date();

        // Create a complete date-time string with timezone information
        // Format: YYYY-MM-DD HH:MM:SS +/-HHMM
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        // Get timezone offset in minutes and convert to hours and minutes
        const tzOffset = now.getTimezoneOffset();
        const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
        const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
        const tzSign = tzOffset <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive UTC offsets

        // Construct the full datetime string with timezone
        const deviceDateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${tzSign}${tzOffsetHours}${tzOffsetMinutes}`;

        console.log('Sending device time:', deviceDateTime);

        // Get the CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Send the device time to the server
        fetch('/store-device-time', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                device_time: deviceDateTime
            })
        })
            .then(response => response.json())
            .then(data => {
                console.log('Device time stored successfully:', data);
            })
            .catch(error => {
                console.error('Error storing device time:', error);
            });
    }

    // Send device time when page loads
    sendDeviceTime();

    // Also send device time when any sign button is clicked
    document.addEventListener('click', function (event) {
        // Check if the clicked element is a sign button
        if (event.target &&
            (event.target.id === 'sign-all-global-btn' ||
                event.target.id === 'sign-selected-global-btn' ||
                event.target.closest('#sign-all-global-btn') ||
                event.target.closest('#sign-selected-global-btn'))) {

            // Send device time before form submission
            sendDeviceTime();
        }
    });
});
